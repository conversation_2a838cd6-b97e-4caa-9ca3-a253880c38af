#!/usr/bin/env python3
"""
测试修改后的好氧池运行参数计算模块
"""

import sys
import os
import logging

# 简化版本的AerobicTankAnalyzer类，用于测试核心计算逻辑
class AerobicTankAnalyzer:
    """
    好氧池运行分析类（简化测试版本）
    """
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def calculate_aerobic_tank_operation(
        self,
        # --- Design Parameters ---
        V: float,              # 好氧池有效体积 (m³)
        R_external: float,     # 外回流比（保留参数但不用于计算）

        # --- Yesterday's Data ---
        Qy: float,             # 上一天处理水量 (m³/day)
        Sy_BOD: float,         # 上一天进水BOD浓度 (mg/L)
        Xy_MLSS: float,        # 上一天好氧池MLSS浓度 (mg/L)

        # --- Today's Data ---
        Qt: float,             # 今天当前处理水量 (m³/day) - 不用于计算
        St_BOD: float,         # 今天当前进水BOD浓度 (mg/L)
        St_BOD_effluent: float, # 今天当前出水BOD浓度 (mg/L)
        Xt_MLSS_actual: float, # 今天当前好氧池MLSS浓度 (mg/L)
        Qw: float,             # 单台排泥泵流量 (m³/hour)
        num_pumps: int,        # 计划用于排泥的泵数量
        TNt: float = None,     # 今天当前出水总氮 (mg/L)
    ) -> dict:
        """
        使用专业工艺计算方式计算排泥需求和时间
        """
        results = {
            'F_M_target': 0.111,  # 固定目标污泥负荷
            'X_target': None,
            'Vincrease': None,  # 污泥增量
            'delta_X_mass': None,
            'Xp': None,  # 所需排泥干重
            'wasting_needed': False,
            'MLSSv': None,  # 回流污泥浓度
            'total_Qw': None,
            'wasting_time_hours': 0.0,
            'analysis': [],
            'recommendation': ""
        }

        # --- Step 1: 计算污泥增量 Vincrease = Y*(S1-S2)*Qy ---
        if V <= 0:
            results['analysis'].append("错误：好氧池体积(V)不能为零或负数。")
            results['recommendation'] = "请检查输入的设计参数。"
            return results

        try:
            # 污泥增量计算，Y=1.25为产泥系数
            Y = 1.25  # 产泥系数
            Vincrease = Y * (St_BOD - St_BOD_effluent) * Qy
            results['Vincrease'] = Vincrease
            results['analysis'].append(f"1. 计算污泥增量：Vincrease = {Y} × ({St_BOD:.2f} - {St_BOD_effluent:.2f}) × {Qy:.2f} = {Vincrease:.2f} kg")

            # 设定固定的目标污泥负荷
            results['analysis'].append(f"2. 使用设计目标污泥负荷：F/M = {results['F_M_target']:.3f} kgBOD/(kgMLSS·d)")

        except Exception as e:
            results['analysis'].append(f"错误：计算污泥增量时发生错误: {e}")
            results['recommendation'] = "请检查输入的BOD数据和处理水量。"
            return results

        # --- Step 2: 计算目标MLSS浓度 Xtarget = Qy*S1/V/(F/M) ---
        try:
            # 使用昨天的处理水量和今天的进水BOD浓度计算目标MLSS
            X_target = (Qy * St_BOD) / (V * results['F_M_target'])
            results['X_target'] = X_target
            results['analysis'].append(f"3. 计算目标MLSS浓度：Xtarget = {Qy:.2f} × {St_BOD:.2f} / ({V:.2f} × {results['F_M_target']:.3f}) = {X_target:.2f} mg/L")
        except ZeroDivisionError:
            results['analysis'].append("错误：计算目标MLSS时发生除零错误。")
            results['recommendation'] = "池体积或目标污泥负荷不能为零。"
            return results
        except Exception as e:
            results['analysis'].append(f"错误：计算目标MLSS时发生错误: {e}")
            results['recommendation'] = "请检查输入数据。"
            return results

        # --- Step 3: 计算需要排出的污泥总量 !X = (Xactual-Xtarget)*V ---
        delta_X_mass = (Xt_MLSS_actual - results['X_target']) * V
        results['delta_X_mass'] = delta_X_mass

        results['analysis'].append(f"4. 当前实际MLSS浓度：{Xt_MLSS_actual:.2f} mg/L")
        results['analysis'].append(f"5. 计算需要排出的污泥总量：!X = ({Xt_MLSS_actual:.2f} - {results['X_target']:.2f}) × {V:.2f} = {delta_X_mass:.2f} kg")

        # --- Step 4: 计算所需排泥干重 Xp = !X + Vincrease ---
        Xp = delta_X_mass + results['Vincrease']
        results['Xp'] = Xp
        results['analysis'].append(f"6. 计算所需排泥干重：Xp = {delta_X_mass:.2f} + {results['Vincrease']:.2f} = {Xp:.2f} kg")

        # --- Step 5: 判断是否需要排泥 ---
        if Xp <= 0:
            results['wasting_needed'] = False
            results['analysis'].append("7. 判断结果：Xp ≤ 0，不需要进行排泥")
            if delta_X_mass <= 0:
                results['recommendation'] = "当前MLSS浓度低于目标浓度，无需排泥。建议继续监测MLSS浓度变化。"
                if Xt_MLSS_actual < results['X_target'] * 0.9:
                    results['recommendation'] += " 当前MLSS显著低于目标值，建议检查是否有污泥流失。"
            else:
                results['recommendation'] = "虽然当前MLSS略高于目标值，但考虑到污泥增量，暂时无需排泥。"
        else:
            results['wasting_needed'] = True
            results['analysis'].append("7. 判断结果：Xp > 0，需要进行排泥")

            # --- Step 6: 计算回流污泥浓度 MLSSv = 2 * Xactual ---
            MLSSv = 2 * Xt_MLSS_actual
            results['MLSSv'] = MLSSv
            results['analysis'].append(f"8. 计算回流污泥浓度：MLSSv = 2 × {Xt_MLSS_actual:.2f} = {MLSSv:.2f} mg/L")

            # --- Step 7: 计算排泥时间 T = Xp / (MLSSv * Qw) ---
            if Qw <= 0 or num_pumps <= 0:
                results['analysis'].append("错误：排泥泵流量或数量为零或负数。")
                results['recommendation'] = "请提供有效的排泥泵参数。"
                results['wasting_time_hours'] = None
                return results

            total_Qw = Qw * num_pumps
            results['total_Qw'] = total_Qw

            try:
                # 使用新的计算公式：T = Xp / (MLSSv * Qw)
                wasting_time_hours = Xp / (MLSSv * total_Qw)
                results['wasting_time_hours'] = wasting_time_hours

                results['analysis'].append(f"9. 计算排泥时间：T = {Xp:.2f} / ({MLSSv:.2f} × {total_Qw:.2f}) = {wasting_time_hours:.2f} 小时")

                # --- 生成操作建议 ---
                recommendation = f"根据专业工艺计算，建议启动 {num_pumps} 台排泥泵，运行约 {wasting_time_hours:.2f} 小时。"
                recommendation += f" 目标是将MLSS浓度从当前的 {Xt_MLSS_actual:.2f} mg/L 降至目标值 {results['X_target']:.2f} mg/L。"
                recommendation += f" 排泥期间请密切监测MLSS浓度变化和出水水质指标。"

                # --- 考虑出水总氮的影响 ---
                if TNt is not None and TNt > 15:
                     recommendation += f"\n**注意**: 当前出水总氮 (TN={TNt:.2f} mg/L) 较高。"
                     recommendation += f" 建议适当减少排泥时间至 {wasting_time_hours*0.8:.2f} 小时（约8折），"
                     recommendation += f" 以保持较高的污泥龄，促进硝化反硝化过程。"

                results['recommendation'] = recommendation

            except ZeroDivisionError:
                results['analysis'].append("错误：计算排泥时间时发生除零错误。")
                results['recommendation'] = "排泥泵总流量或回流污泥浓度计算为零。"
                results['wasting_time_hours'] = None
            except Exception as e:
                 results['analysis'].append(f"错误：计算排泥时间时发生错误: {e}")
                 results['recommendation'] = "计算排泥时间时发生错误，请检查输入数据。"
                 results['wasting_time_hours'] = None

        # Join analysis points into a single string
        results['analysis'] = "\n".join(results['analysis'])

        return results

def test_professional_calculation():
    """测试专业工艺计算方式"""
    print("=== 测试专业工艺计算方式 ===")
    
    # 创建分析器
    analyzer = AerobicTankAnalyzer()
    
    # 测试数据
    test_params = {
        'V': 44437.5,           # 好氧池有效体积 (m³)
        'R_external': 0.7,      # 外回流比（保留但不用于计算）
        'Qy': 50000,            # 昨天处理水量 (m³/day)
        'Sy_BOD': 120,          # 昨天进水BOD浓度 (mg/L)
        'Xy_MLSS': 3500,        # 昨天好氧池MLSS浓度 (mg/L)
        'Qt': 52000,            # 今天处理水量（不用于计算）
        'St_BOD': 130,          # 今天进水BOD浓度 (mg/L)
        'St_BOD_effluent': 15,  # 今天出水BOD浓度 (mg/L)
        'Xt_MLSS_actual': 4200, # 今天实际MLSS浓度 (mg/L)
        'Qw': 75,               # 单台排泥泵流量 (m³/hour)
        'num_pumps': 1,         # 排泥泵数量
        'TNt': 12               # 出水总氮 (mg/L)
    }
    
    print("输入参数:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")
    print()
    
    # 执行计算
    try:
        result = analyzer.calculate_aerobic_tank_operation(**test_params)
        
        print("计算结果:")
        print(f"  目标污泥负荷 (F/M): {result.get('F_M_target', 'N/A')}")
        print(f"  目标MLSS浓度: {result.get('X_target', 'N/A'):.2f} mg/L")
        print(f"  污泥增量: {result.get('Vincrease', 'N/A'):.2f} kg")
        print(f"  需要排出的污泥总量: {result.get('delta_X_mass', 'N/A'):.2f} kg")
        print(f"  所需排泥干重: {result.get('Xp', 'N/A'):.2f} kg")
        print(f"  是否需要排泥: {result.get('wasting_needed', 'N/A')}")
        print(f"  回流污泥浓度: {result.get('MLSSv', 'N/A'):.2f} mg/L")
        print(f"  排泥时间: {result.get('wasting_time_hours', 'N/A'):.2f} 小时")
        print()
        
        print("详细分析过程:")
        print(result.get('analysis', ''))
        print()
        
        print("操作建议:")
        print(result.get('recommendation', ''))
        
    except Exception as e:
        print(f"计算过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_range_analysis():
    """测试范围分析功能"""
    print("\n" + "="*60)
    print("=== 测试范围分析功能 ===")
    
    analyzer = AerobicTankAnalyzer()
    
    try:
        analysis, recommendation = analyzer.analyze_aerobic_tank_range(
            V=44437.5,
            Qy=50000,
            Sy_BOD=120,
            Xy_MLSS=3500,
            Qt=52000,
            St_BOD=130,
            St_BOD_effluent=15,
            Xt_MLSS_actual=4200,
            Qw=75,
            num_pumps=1,
            TNt=12
        )
        
        print("分析过程:")
        print(analysis)
        print()
        print("建议:")
        print(recommendation)
        
    except Exception as e:
        print(f"范围分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_professional_calculation()
    test_range_analysis()
